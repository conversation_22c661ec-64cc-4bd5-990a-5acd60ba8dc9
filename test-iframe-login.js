#!/usr/bin/env node

import { BrowserAutomation } from './dist/mcp-server.js';

async function testIframeLogin() {
  const automation = new BrowserAutomation({ headless: false });
  
  try {
    console.log('🔍 Testing OAuth Iframe Login');
    console.log('=============================');
    
    // Step 1: Navigate to the staging site
    console.log('\n📍 Step 1: Navigate to staging site...');
    const navResult = await automation.navigate('https://oe-staging5.paytm.com/');
    console.log('✅', navResult);
    
    // Step 2: Wait for iframe to load
    console.log('\n⏳ Step 2: Waiting for OAuth iframe to load...');
    const iframeWaitResult = await automation.waitFor({ 
      selector: '#oauth-iframe', 
      time: 10 
    });
    console.log('✅', iframeWaitResult);
    
    // Step 3: Switch to iframe and analyze content
    console.log('\n🔄 Step 3: Switching to OAuth iframe...');
    
    const iframeAnalysis = await automation.page.evaluate(async () => {
      const iframe = document.querySelector('#oauth-iframe');
      if (!iframe) {
        return { error: 'Iframe not found' };
      }
      
      // Wait a bit for iframe to load
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      try {
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
        
        if (!iframeDoc) {
          return { error: 'Cannot access iframe content (cross-origin)' };
        }
        
        const analysis = {
          title: iframeDoc.title,
          bodyText: iframeDoc.body?.textContent?.trim().slice(0, 500) || '',
          inputs: [],
          buttons: [],
          forms: []
        };
        
        // Get inputs
        iframeDoc.querySelectorAll('input').forEach((input, index) => {
          analysis.inputs.push({
            index,
            type: input.type || '',
            name: input.name || '',
            placeholder: input.placeholder || '',
            id: input.id || '',
            classes: input.className || ''
          });
        });
        
        // Get buttons
        iframeDoc.querySelectorAll('button').forEach((button, index) => {
          analysis.buttons.push({
            index,
            text: button.textContent?.trim() || '',
            type: button.type || '',
            id: button.id || '',
            classes: button.className || ''
          });
        });
        
        // Get forms
        iframeDoc.querySelectorAll('form').forEach((form, index) => {
          analysis.forms.push({
            index,
            action: form.action || '',
            method: form.method || '',
            id: form.id || '',
            classes: form.className || ''
          });
        });
        
        return analysis;
      } catch (e) {
        return { error: `Error accessing iframe: ${e.message}` };
      }
    });
    
    console.log('\n📊 Iframe Analysis Results:');
    console.log('===========================');
    
    if (iframeAnalysis.error) {
      console.log(`❌ ${iframeAnalysis.error}`);
      
      // If we can't access iframe content, try clicking the main button first
      console.log('\n🖱️  Step 4: Trying to click main button to trigger login...');
      const clickResult = await automation.click('main button', 'element-0');
      console.log('✅', clickResult);
      
      // Wait for changes
      await automation.waitFor({ time: 5 });
      
      // Try to get snapshot again
      console.log('\n📸 Step 5: Getting snapshot after button click...');
      const snapshot = await automation.getSnapshot();
      console.log('✅ Snapshot after click:');
      console.log(snapshot);
      
    } else {
      console.log(`Title: ${iframeAnalysis.title}`);
      console.log(`Body Text: ${iframeAnalysis.bodyText}`);
      
      console.log(`\nInputs in iframe: ${iframeAnalysis.inputs.length}`);
      iframeAnalysis.inputs.forEach(input => {
        console.log(`  - Input ${input.index}: type="${input.type}" name="${input.name}" placeholder="${input.placeholder}"`);
      });
      
      console.log(`\nButtons in iframe: ${iframeAnalysis.buttons.length}`);
      iframeAnalysis.buttons.forEach(button => {
        console.log(`  - Button ${button.index}: text="${button.text}" type="${button.type}"`);
      });
      
      console.log(`\nForms in iframe: ${iframeAnalysis.forms.length}`);
      iframeAnalysis.forms.forEach(form => {
        console.log(`  - Form ${form.index}: action="${form.action}" method="${form.method}"`);
      });
    }
    
    // Step 6: Try alternative approach - look for login text in main page
    console.log('\n🔍 Step 6: Looking for login-related text in main page...');
    const textSearch = await automation.page.evaluate(() => {
      const allText = document.body.textContent || '';
      const loginKeywords = ['login', 'sign in', 'email', 'password', 'phone', 'mobile', 'otp', 'enter'];
      const foundKeywords = loginKeywords.filter(keyword => 
        allText.toLowerCase().includes(keyword.toLowerCase())
      );
      return {
        allText: allText.slice(0, 1000),
        foundKeywords
      };
    });
    
    console.log(`Found keywords: ${textSearch.foundKeywords.join(', ')}`);
    console.log(`Page text preview: ${textSearch.allText}`);
    
    // Step 7: Take final screenshot
    console.log('\n📷 Step 7: Taking final screenshot...');
    const screenshotResult = await automation.takeScreenshot('iframe-login-test.png');
    console.log('✅', screenshotResult);
    
    console.log('\n🎉 Test completed!');
    
  } catch (error) {
    console.error('❌ Error during test:', error);
  } finally {
    console.log('\n🧹 Cleaning up...');
    await automation.cleanup();
  }
}

testIframeLogin();
