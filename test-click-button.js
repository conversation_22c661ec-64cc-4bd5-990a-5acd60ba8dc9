#!/usr/bin/env node

import { BrowserAutomation } from './dist/mcp-server.js';

async function testClickButton() {
  const automation = new BrowserAutomation({ headless: false });
  
  try {
    console.log('🚀 Testing Button Click on Staging Site');
    console.log('=======================================');
    
    // Step 1: Navigate to the staging site
    console.log('\n📍 Step 1: Navigate to staging site...');
    const navResult = await automation.navigate('https://oe-staging5.paytm.com/');
    console.log('✅', navResult);
    
    // Step 2: Get initial snapshot
    console.log('\n📸 Step 2: Get initial snapshot...');
    const snapshot1 = await automation.getSnapshot();
    console.log('✅ Initial snapshot:');
    console.log(snapshot1);
    
    // Step 3: Click on the button we found
    console.log('\n🖱️  Step 3: Clicking on the button...');
    const clickResult = await automation.click('clickable button', 'element-0');
    console.log('✅', clickResult);
    
    // Step 4: Wait for page changes after click
    console.log('\n⏳ Step 4: Waiting for page changes...');
    await automation.waitFor({ time: 3 });
    
    // Step 5: Get snapshot after click to see what changed
    console.log('\n📸 Step 5: Get snapshot after click...');
    const snapshot2 = await automation.getSnapshot();
    console.log('✅ Snapshot after click:');
    console.log(snapshot2);
    
    // Step 6: Look for login form elements that might have appeared
    console.log('\n🔍 Step 6: Looking for login form elements...');
    const loginSelectors = [
      'input[type="email"]',
      'input[type="text"]',
      'input[type="password"]',
      'input[name*="email"]',
      'input[name*="phone"]',
      'input[placeholder*="email"]',
      'input[placeholder*="phone"]',
      'input[placeholder*="mobile"]'
    ];
    
    for (const selector of loginSelectors) {
      console.log(`   Checking for: ${selector}`);
      const waitResult = await automation.waitFor({ selector, time: 2 });
      console.log(`   Result: ${waitResult}`);
      
      if (waitResult.includes('Successfully waited')) {
        console.log(`   ✅ Found login element: ${selector}`);
        
        // Take another snapshot to see the login form
        console.log('\n📸 Getting snapshot with login form...');
        const loginSnapshot = await automation.getSnapshot();
        console.log('✅ Login form snapshot:');
        console.log(loginSnapshot);
        break;
      }
    }
    
    // Step 7: Take final screenshot
    console.log('\n📷 Step 7: Taking final screenshot...');
    const screenshotResult = await automation.takeScreenshot('after-button-click.png');
    console.log('✅', screenshotResult);
    
    console.log('\n🎉 Test completed! Check the screenshots to see the progression.');
    
  } catch (error) {
    console.error('❌ Error during test:', error);
  } finally {
    console.log('\n🧹 Cleaning up...');
    await automation.cleanup();
  }
}

testClickButton();
