version: '3.8'

services:
  selenium-mcp:
    build: .
    ports:
      - "8931:8931"
    environment:
      - DISPLAY=:99
      - CHROME_BIN=/usr/bin/google-chrome
      - CHROME_PATH=/usr/bin/google-chrome
    volumes:
      - ./screenshots:/app/screenshots
      - ./pdfs:/app/pdfs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8931/"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - selenium-mcp-network

networks:
  selenium-mcp-network:
    driver: bridge
