#!/usr/bin/env node

import { BrowserAutomation } from './dist/mcp-server.js';

async function analyzePageContent() {
  const automation = new BrowserAutomation({ headless: false });
  
  try {
    console.log('🔍 Comprehensive Page Analysis');
    console.log('==============================');
    
    // Step 1: Navigate to the staging site
    console.log('\n📍 Step 1: Navigate to staging site...');
    const navResult = await automation.navigate('https://oe-staging5.paytm.com/');
    console.log('✅', navResult);
    
    // Step 2: Get page content analysis
    console.log('\n📄 Step 2: Analyzing page content...');
    
    // Custom evaluation to get more detailed page information
    const pageAnalysis = await automation.page.evaluate(() => {
      const analysis = {
        url: window.location.href,
        title: document.title,
        bodyText: document.body.textContent?.trim().slice(0, 500) || '',
        allElements: [],
        forms: [],
        inputs: [],
        buttons: [],
        links: [],
        scripts: [],
        iframes: []
      };
      
      // Get all elements with any kind of interaction potential
      const allInteractive = document.querySelectorAll('*');
      Array.from(allInteractive).slice(0, 50).forEach((el, index) => {
        const tagName = el.tagName.toLowerCase();
        const text = el.textContent?.trim().slice(0, 100) || '';
        const classes = el.className || '';
        const id = el.id || '';
        
        if (text || classes || id || ['input', 'button', 'a', 'form', 'div', 'span'].includes(tagName)) {
          analysis.allElements.push({
            index,
            tag: tagName,
            text: text,
            classes: classes,
            id: id,
            attributes: Array.from(el.attributes).map(attr => `${attr.name}="${attr.value}"`).join(' ')
          });
        }
      });
      
      // Specific element types
      document.querySelectorAll('form').forEach((form, index) => {
        analysis.forms.push({
          index,
          action: form.action || '',
          method: form.method || '',
          classes: form.className || '',
          id: form.id || ''
        });
      });
      
      document.querySelectorAll('input').forEach((input, index) => {
        analysis.inputs.push({
          index,
          type: input.type || '',
          name: input.name || '',
          placeholder: input.placeholder || '',
          classes: input.className || '',
          id: input.id || ''
        });
      });
      
      document.querySelectorAll('button').forEach((button, index) => {
        analysis.buttons.push({
          index,
          text: button.textContent?.trim() || '',
          type: button.type || '',
          classes: button.className || '',
          id: button.id || ''
        });
      });
      
      document.querySelectorAll('a').forEach((link, index) => {
        analysis.links.push({
          index,
          text: link.textContent?.trim() || '',
          href: link.href || '',
          classes: link.className || '',
          id: link.id || ''
        });
      });
      
      document.querySelectorAll('script').forEach((script, index) => {
        analysis.scripts.push({
          index,
          src: script.src || '',
          type: script.type || '',
          hasContent: !!script.textContent?.trim()
        });
      });
      
      document.querySelectorAll('iframe').forEach((iframe, index) => {
        analysis.iframes.push({
          index,
          src: iframe.src || '',
          classes: iframe.className || '',
          id: iframe.id || ''
        });
      });
      
      return analysis;
    });
    
    console.log('\n📊 Page Analysis Results:');
    console.log('========================');
    console.log(`URL: ${pageAnalysis.url}`);
    console.log(`Title: ${pageAnalysis.title}`);
    console.log(`Body Text Preview: ${pageAnalysis.bodyText}`);
    console.log(`\nForms found: ${pageAnalysis.forms.length}`);
    pageAnalysis.forms.forEach(form => {
      console.log(`  - Form ${form.index}: action="${form.action}" method="${form.method}" class="${form.classes}" id="${form.id}"`);
    });
    
    console.log(`\nInputs found: ${pageAnalysis.inputs.length}`);
    pageAnalysis.inputs.forEach(input => {
      console.log(`  - Input ${input.index}: type="${input.type}" name="${input.name}" placeholder="${input.placeholder}" class="${input.classes}" id="${input.id}"`);
    });
    
    console.log(`\nButtons found: ${pageAnalysis.buttons.length}`);
    pageAnalysis.buttons.forEach(button => {
      console.log(`  - Button ${button.index}: text="${button.text}" type="${button.type}" class="${button.classes}" id="${button.id}"`);
    });
    
    console.log(`\nLinks found: ${pageAnalysis.links.length}`);
    pageAnalysis.links.slice(0, 5).forEach(link => {
      console.log(`  - Link ${link.index}: text="${link.text}" href="${link.href}" class="${link.classes}"`);
    });
    
    console.log(`\nScripts found: ${pageAnalysis.scripts.length}`);
    pageAnalysis.scripts.slice(0, 5).forEach(script => {
      console.log(`  - Script ${script.index}: src="${script.src}" type="${script.type}" hasContent=${script.hasContent}`);
    });
    
    console.log(`\nIframes found: ${pageAnalysis.iframes.length}`);
    pageAnalysis.iframes.forEach(iframe => {
      console.log(`  - Iframe ${iframe.index}: src="${iframe.src}" class="${iframe.classes}" id="${iframe.id}"`);
    });
    
    console.log(`\nAll Elements (first 10):`);
    pageAnalysis.allElements.slice(0, 10).forEach(el => {
      console.log(`  - ${el.tag} #${el.index}: text="${el.text}" class="${el.classes}" id="${el.id}"`);
    });
    
    // Step 3: Take screenshot for visual reference
    console.log('\n📷 Step 3: Taking screenshot...');
    const screenshotResult = await automation.takeScreenshot('page-analysis.png');
    console.log('✅', screenshotResult);
    
    console.log('\n🎉 Analysis completed!');
    
  } catch (error) {
    console.error('❌ Error during analysis:', error);
  } finally {
    console.log('\n🧹 Cleaning up...');
    await automation.cleanup();
  }
}

analyzePageContent();
