{"name": "selenium-mcp-server", "version": "0.1.8", "description": "Model Context Protocol server for Selenium WebDriver - enables LLMs to control web browsers", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"selenium-mcp": "dist/index.js"}, "scripts": {"build": "node scripts/build.js", "build:ts": "tsc", "dev": "tsx src/index.ts", "prepare": "npm run build", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "prepublishOnly": "npm run build"}, "keywords": ["mcp", "model-context-protocol", "selenium", "webdriver", "browser-automation", "llm", "ai", "cursor"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/Ra<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>/selenium-mcp-server.git"}, "bugs": {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>/selenium-mcp-server/issues"}, "homepage": "https://github.com/<PERSON><PERSON><PERSON><PERSON>-<PERSON>gh<PERSON><PERSON><PERSON>/selenium-mcp-server#readme", "engines": {"node": ">=18.0.0"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.4.0", "commander": "^11.0.0", "cross-spawn": "^7.0.3", "puppeteer": "^22.0.0", "zod": "^3.25.32", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@types/cross-spawn": "^6.0.2", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "prettier": "^3.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "files": ["dist/", "lib/", "README.md", "LICENSE", "CHANGELOG.md"]}