#!/usr/bin/env node

import { BrowserAutomation } from './dist/mcp-server.js';

async function testFrameSwitching() {
  const automation = new BrowserAutomation({ headless: false });
  
  try {
    console.log('🔄 Testing Frame Switching Capabilities');
    console.log('======================================');
    
    // Step 1: Navigate to the staging site
    console.log('\n📍 Step 1: Navigate to staging site...');
    const navResult = await automation.navigate('https://oe-staging5.paytm.com/');
    console.log('✅', navResult);
    
    // Step 2: Get initial snapshot of main page
    console.log('\n📸 Step 2: Get main page snapshot...');
    const mainSnapshot = await automation.getSnapshot();
    console.log('✅ Main page snapshot:');
    console.log(mainSnapshot);
    
    // Step 3: List all available frames
    console.log('\n🔍 Step 3: List all available frames...');
    const framesResult = await automation.getFrames();
    console.log('✅ Available frames:');
    console.log(framesResult);
    
    // Step 4: Try to switch to the OAuth iframe
    console.log('\n🔄 Step 4: Switching to OAuth iframe...');
    const switchResult = await automation.switchFrame({ frameSelector: '#oauth-iframe' });
    console.log('✅ Switch result:', switchResult);
    
    // Step 5: Get snapshot of iframe content (if accessible)
    console.log('\n📸 Step 5: Get iframe snapshot...');
    const iframeSnapshot = await automation.getSnapshot();
    console.log('✅ Iframe snapshot:');
    console.log(iframeSnapshot);
    
    // Step 6: Try switching by frame index as alternative
    console.log('\n🔄 Step 6: Trying to switch by frame index...');
    const switchByIndexResult = await automation.switchFrame({ frameIndex: 1 });
    console.log('✅ Switch by index result:', switchByIndexResult);
    
    // Step 7: Get snapshot after switching by index
    console.log('\n📸 Step 7: Get snapshot after index switch...');
    const indexSnapshot = await automation.getSnapshot();
    console.log('✅ Index switch snapshot:');
    console.log(indexSnapshot);
    
    // Step 8: Switch back to main content
    console.log('\n🔄 Step 8: Switching back to main content...');
    const switchBackResult = await automation.switchFrame({ switchToMain: true });
    console.log('✅ Switch back result:', switchBackResult);
    
    // Step 9: Verify we're back on main page
    console.log('\n📸 Step 9: Verify main page snapshot...');
    const finalSnapshot = await automation.getSnapshot();
    console.log('✅ Final main page snapshot:');
    console.log(finalSnapshot);
    
    // Step 10: Take screenshot for visual verification
    console.log('\n📷 Step 10: Taking screenshot...');
    const screenshotResult = await automation.takeScreenshot('frame-switching-test.png');
    console.log('✅', screenshotResult);
    
    console.log('\n🎉 Frame switching test completed!');
    
  } catch (error) {
    console.error('❌ Error during test:', error);
  } finally {
    console.log('\n🧹 Cleaning up...');
    await automation.cleanup();
  }
}

testFrameSwitching();
