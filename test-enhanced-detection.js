#!/usr/bin/env node

import { BrowserAutomation } from './dist/mcp-server.js';

async function testEnhancedDetection() {
  const automation = new BrowserAutomation({ headless: false });
  
  try {
    console.log('🚀 Testing Enhanced Element Detection');
    console.log('=====================================');
    
    // Step 1: Navigate to the staging site
    console.log('\n📍 Step 1: Navigate to staging site...');
    const navResult = await automation.navigate('https://oe-staging5.paytm.com/');
    console.log('✅', navResult);
    
    // Step 2: Get enhanced snapshot with improved detection
    console.log('\n📸 Step 2: Get enhanced page snapshot...');
    const snapshot = await automation.getSnapshot();
    console.log('✅ Enhanced snapshot result:');
    console.log(snapshot);
    
    // Step 3: If no elements found, try waiting for common login selectors
    if (snapshot.includes('No interactive elements detected')) {
      console.log('\n⏳ Step 3: Waiting for login form elements...');
      
      const commonLoginSelectors = [
        'input[type="email"]',
        'input[type="text"]',
        'input[type="password"]',
        'input[name*="email"]',
        'input[name*="username"]',
        'input[name*="login"]',
        'button[type="submit"]',
        '.login-form',
        '#login',
        '[data-testid*="login"]'
      ];
      
      for (const selector of commonLoginSelectors) {
        console.log(`   Trying to wait for: ${selector}`);
        const waitResult = await automation.waitFor({ selector, time: 5 });
        console.log(`   Result: ${waitResult}`);
        
        if (waitResult.includes('Successfully waited')) {
          console.log(`   ✅ Found element with selector: ${selector}`);
          break;
        }
      }
      
      // Take another snapshot after waiting
      console.log('\n📸 Step 4: Get snapshot after waiting...');
      const snapshot2 = await automation.getSnapshot();
      console.log('✅ Second snapshot result:');
      console.log(snapshot2);
    }
    
    // Step 4: Try waiting for specific text that might appear on login page
    console.log('\n⏳ Step 5: Waiting for login-related text...');
    const loginTexts = ['Login', 'Sign In', 'Email', 'Password', 'Username', 'Enter'];
    
    for (const text of loginTexts) {
      console.log(`   Waiting for text: "${text}"`);
      const waitResult = await automation.waitFor({ text, time: 3 });
      console.log(`   Result: ${waitResult}`);
      
      if (waitResult.includes('Successfully waited')) {
        console.log(`   ✅ Found text: "${text}"`);
        break;
      }
    }
    
    // Step 5: Final snapshot
    console.log('\n📸 Step 6: Final snapshot...');
    const finalSnapshot = await automation.getSnapshot();
    console.log('✅ Final snapshot result:');
    console.log(finalSnapshot);
    
    // Step 6: Take a screenshot for visual verification
    console.log('\n📷 Step 7: Taking screenshot for verification...');
    const screenshotResult = await automation.takeScreenshot('staging-site-test.png');
    console.log('✅', screenshotResult);
    
    console.log('\n🎉 Test completed! Check the screenshot to see what the page looks like.');
    
  } catch (error) {
    console.error('❌ Error during test:', error);
  } finally {
    console.log('\n🧹 Cleaning up...');
    await automation.cleanup();
  }
}

testEnhancedDetection();
